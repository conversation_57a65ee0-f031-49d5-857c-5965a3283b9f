def included_line(the_day_before, one_day_after):
    if one_day_after['high'] <= the_day_before['high'] and one_day_after['low'] >= the_day_before['low']:
        return True
    else:
        return False

def read():
    stock_list = []  # 修复语法错误，使用更好的变量名
    # 逐行读取当前目录下的stock_transaction_pair.csv 并依次添加到stock_list
    try:
        with open('stock_transaction_pair.csv', 'r', encoding='utf-8') as f:
            for line in f:
                # 去除换行符、空白字符和BOM字符
                code = line.strip().replace('\ufeff', '')
                if code and len(code) == 6 and code.isdigit():  # 验证股票代码格式
                    stock_list.append(code)
        print(f"✓ 成功读取 {len(stock_list)} 只股票代码")
        return stock_list
    except FileNotFoundError:
        print("❌ 文件 stock_transaction_pair.csv 不存在")
        return []
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return []

if __name__ == '__main__':
    result = read()
    print(f"返回的股票代码数量: {len(result)}")
