# 获取中国A股完整列表 - 多数据源版本
import time

import pandas as pd
import requests

# 设置 pandas 显示选项
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.expand_frame_repr', False)

def get_stocks_from_eastmoney_comprehensive():
    """从东方财富网获取完整A股列表 - 改进版"""
    print("正在从东方财富网获取完整A股列表...")

    all_stocks = []

    # 尝试获取更多页面的数据
    for page in range(1, 101):  # 尝试获取100页
        try:
            print(f"\\r正在获取第 {page} 页数据...", end='', flush=True)

            url = "https://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': page,
                'pz': 100,  # 每页100只
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',  # 所有A股
                'fields': 'f12,f14,f2,f3,f20,f5'
            }

            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'diff' in data['data'] and data['data']['diff']:
                    stocks = data['data']['diff']

                    for stock in stocks:
                        stock_code = stock.get('f12', '')
                        stock_name = stock.get('f14', '')

                        if not stock_code or not stock_name:
                            continue

                        # 跳过已存在的股票
                        if any(s['code'] == stock_code for s in all_stocks):
                            continue

                        # 判断交易所和板块
                        if stock_code.startswith('6'):
                            exchange = 'SSE'
                            exchange_name = '上海证券交易所'
                            if stock_code.startswith('688'):
                                board_type = '科创板'
                            elif stock_code.startswith('689'):
                                board_type = '科创板'
                            else:
                                board_type = '主板'
                        elif stock_code.startswith('0'):
                            exchange = 'SZSE'
                            exchange_name = '深圳证券交易所'
                            board_type = '主板'
                        elif stock_code.startswith('2'):
                            exchange = 'SZSE'
                            exchange_name = '深圳证券交易所'
                            board_type = '中小板'
                        elif stock_code.startswith('3'):
                            exchange = 'SZSE'
                            exchange_name = '深圳证券交易所'
                            board_type = '创业板'
                        else:
                            continue  # 跳过非A股

                        stock_info = {
                            'code': stock_code,
                            'name': stock_name,
                            'exchange': exchange,
                            'exchange_name': exchange_name,
                            'board_type': board_type,
                            'tradingview_symbol': f"'{exchange},{stock_code}",
                            'price': stock.get('f2', 0),
                            'change_pct': stock.get('f3', 0),
                            'market_cap': stock.get('f20', 0)
                        }
                        all_stocks.append(stock_info)

                    # 如果这一页没有新数据，可能已经到底了
                    if len(stocks) < 100:
                        print(f"\\n第 {page} 页只有 {len(stocks)} 只股票，可能已获取完毕")
                        break

                    time.sleep(0.1)  # 短暂延迟
                else:
                    print(f"\\n第 {page} 页没有数据")
                    break
            else:
                print(f"\\n第 {page} 页请求失败: {response.status_code}")
                break

        except Exception as e:
            print(f"\\n第 {page} 页出错: {e}")
            break

    print(f"\\n从东方财富网获取到 {len(all_stocks)} 只股票")
    return all_stocks


def merge_and_deduplicate(stock_lists):
    """合并多个数据源并去重"""
    print("\\n正在合并和去重数据...")

    all_stocks = []
    seen_codes = set()

    for stocks in stock_lists:
        for stock in stocks:
            code = stock['code']
            if code not in seen_codes:
                all_stocks.append(stock)
                seen_codes.add(code)

    print(f"合并后共有 {len(all_stocks)} 只不重复的股票")
    return all_stocks

def analyze_final_data(stocks):
    """分析最终数据"""
    if not stocks:
        print("没有股票数据可分析")
        return

    df = pd.DataFrame(stocks)
    print(f"📊 总股票数量: {len(stocks)}")

    # 按交易所统计
    print("\\n🏢 按交易所统计:")
    exchange_stats = df.groupby(['exchange', 'exchange_name']).size().reset_index(name='数量')
    for _, row in exchange_stats.iterrows():
        print(f"   {row['exchange_name']} ({row['exchange']}): {row['数量']} 只")

    # 按板块统计
    print("\\n📈 按板块统计:")
    board_stats = df.groupby('board_type').size().sort_values(ascending=False)
    for board, count in board_stats.items():
        print(f"   {board}: {count} 只")

    # 代码分布
    print("\\n🔢 股票代码分布:")
    for exchange in ['SSE', 'SZSE']:
        exchange_stocks = df[df['exchange'] == exchange]
        if not exchange_stocks.empty:
            exchange_name = exchange_stocks['exchange_name'].iloc[0]
            codes = sorted(exchange_stocks['code'].tolist())
            print(f"\\n   {exchange_name}:")
            print(f"     总数: {len(codes)} 只")
            if codes:
                print(f"     代码范围: {codes[0]} - {codes[-1]}")

                # 按开头分组
                prefixes = {}
                for code in codes:
                    prefix = code[:1]
                    prefixes[prefix] = prefixes.get(prefix, 0) + 1

                print("     按代码开头分布:")
                for prefix in sorted(prefixes.keys()):
                    count = prefixes[prefix]
                    if prefix == '6':
                        desc = "主板/科创板"
                    elif prefix == '0':
                        desc = "主板"
                    elif prefix == '2':
                        desc = "中小板"
                    elif prefix == '3':
                        desc = "创业板"
                    else:
                        desc = "其他"
                    print(f"       {prefix}开头 ({desc}): {count} 只")

def save_final_data(stocks):
    """保存最终数据"""
    if not stocks:
        print("没有数据可保存")
        return {}

    df = pd.DataFrame(stocks)

    print("\\n" + "="*80)
    print("保存完整A股数据")
    print("="*80)

    files_created = {}

    # 保存 tradingview_symbol 列表，不使用 CSV 格式避免引号问题
    filename_ultra = 'tradingview_symbol.txt'

    # 直接写入文本文件，每行一个 symbol
    with open(filename_ultra, 'w', encoding='utf-8') as f:
        for stock in stocks:
            f.write(stock['tradingview_symbol'] + '\n')

    files_created['ultra_simple'] = filename_ultra
    print(f"✅ TradingView符号列表: {filename_ultra} (共 {len(stocks)} 个符号)")

    return files_created

def display_final_samples(stocks):
    """显示最终样本"""
    if not stocks:
        return

    df = pd.DataFrame(stocks)
    # 按交易所显示
    for exchange in ['SSE', 'SZSE']:
        exchange_df = df[df['exchange'] == exchange]
        if not exchange_df.empty:
            exchange_name = exchange_df['exchange_name'].iloc[0]


def main():
    """主函数"""
    print("="  )

    all_stock_lists = []

    #  从东方财富网获取
    eastmoney_stocks = get_stocks_from_eastmoney_comprehensive()
    if eastmoney_stocks:
        all_stock_lists.append(eastmoney_stocks)
    # 4. 合并和去重
    final_stocks = merge_and_deduplicate(all_stock_lists)

    if not final_stocks:
        print("❌ 未能获取到任何股票数据")
        return

    # 5. 分析数据
    analyze_final_data(final_stocks)

    # 6. 显示样本
    display_final_samples(final_stocks)

    # 7. 保存数据
    saved_files = save_final_data(final_stocks)

    # 8. 最终说明
    print("="*100)
    for file_type, filename in saved_files.items():
        descriptions = {
            'sse': '上海证券交易所A股',
            'szse': '深圳证券交易所A股'
        }
        desc = descriptions.get(file_type, filename)
    print("   🏢 上海证券交易所（SSE）:")
    print("      • 60xxxx - 主板股票")
    print("      • 688xxx - 科创板股票")
    print("   🏢 深圳证券交易所（SZSE）:")
    print("      • 00xxxx - 主板股票")
    print("      • 20xxxx - 中小板股票")
    print("      • 30xxxx - 创业板股票")
    print("   • TradingView符号列已经为您格式化好，可直接使用")

if __name__ == "__main__":
    main()
