# 导入 pandas 库用于数据处理和分析
import pandas as pd
import sys
import os
from huobiTradingPair import TvDatafeed

# 添加项目根目录到 Python 路径，确保能找到 huobiTradingPair 模块
# 当前文件路径: coin/coin-server/get_huobi_data.py
# 需要到达: analysis-system/
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置 pandas 显示选项，使数据输出更易读
pd.set_option('display.max_rows', None)  # 显示所有行，不截断
pd.set_option('display.max_columns', None)  # 显示所有列，不截断
pd.set_option('display.width', 1000)  # 设置显示宽度为1000个字符
pd.set_option('display.expand_frame_repr', False)  # 禁用数据框的自动换行

# 初始化 TvDatafeed 对象，不使用登录凭证
print(f"ℹ️ Python 版本: {sys.version}")

tv = TvDatafeed()

# 从币安交易所获取比特币数据（已注释）
# data = tv.get_hist('BTCUSDT', 'BINANCE', n_bars=11)

# 获取中国A股数据 - 002594比亚迪股票
# 002594是深圳证券交易所的股票，应该使用SZSE
# 如果是上海证券交易所的股票（6开头），应该使用SSE
data = tv.get_hist('002594', 'SZSE', n_bars=10)
# 从深圳证券交易所获取002594（比亚迪）


# 打印获取到的数据
if data is not None:
    print(data)
    print(f"ℹ️ 数据类型: {type(data)}")
    if hasattr(data, 'shape'):
        print(f"ℹ️ 数据形状: {data.shape}")
else:
    print("❌ 无法获取任何数据")
