# 简化版本 - 解决 BrokenPipeError 问题
import pandas as pd
import sys
import os
import time

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置 pandas 显示选项
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 2000)
pd.set_option('display.expand_frame_repr', False)

def safe_get_stock_data(symbol='002594', exchange='SZSE', n_bars=3):
    """
    安全的股票数据获取函数
    """
    try:
        print(f"🔄 正在获取 {symbol} ({exchange}) 的数据...")
        
        # 导入并初始化 TvDatafeed
        from huobiTradingPair import TvDatafeed
        tv = TvDatafeed()
        
        # 获取数据
        data = tv.get_hist(symbol, exchange, n_bars=n_bars)
        
        if data is not None and not data.empty:
            print(f"✅ 成功获取数据！")
            return data
        else:
            print(f"⚠️ 获取到空数据")
            return None
            
    except BrokenPipeError:
        print("❌ 网络连接中断 (BrokenPipeError)")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 稍后重试")
        print("   3. 尝试使用 VPN")
        return None
        
    except ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        print("💡 可能的原因:")
        print("   1. TradingView 服务器暂时不可用")
        print("   2. 网络防火墙阻止连接")
        return None
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请检查 huobiTradingPair 模块是否正确安装")
        return None
        
    except Exception as e:
        print(f"❌ 未知错误: {type(e).__name__}: {e}")
        return None

def main():
    print("🚀 安全的股票数据获取测试")
    print("=" * 50)
    
    # 测试获取数据
    data = safe_get_stock_data('002594', 'SZSE', 3)
    
    if data is not None:
        print("\n📊 获取到的数据:")
        print(data.to_string())
        
        print(f"\n📈 基本信息:")
        print(f"   • 股票代码: {data['symbol'].iloc[0] if 'symbol' in data.columns else '002594'}")
        print(f"   • 数据行数: {len(data)}")
        print(f"   • 最新收盘价: {data['close'].iloc[-1]:.2f}")
        
    else:
        print("\n💔 数据获取失败")
        print("\n🔧 故障排除建议:")
        print("1. 检查网络连接是否正常")
        print("2. 确认防火墙没有阻止连接")
        print("3. 尝试更换网络环境")
        print("4. 稍后重试（TradingView 可能暂时限制访问）")
        print("5. 考虑使用 TradingView 账号登录")

if __name__ == "__main__":
    main()
