# 测试 f-string 格式化语法
exchange = "SSE"
stock_code = "600000"

print("测试 f-string 格式化语法：")
print("="*50)

# 方式1：原始格式
format1 = f"'{exchange},{stock_code}'"
print(f"方式1 (原始): {format1}")

# 方式2：正确的带花括号格式
format2 = f"'{{{exchange},{stock_code}}}'"
print(f"方式2 (带花括号): {format2}")

# 方式3：使用 .format() 方法
format3 = "'{{{},{}}}'".format(exchange, stock_code)
print(f"方式3 (.format方法): {format3}")

print("\n解释:")
print("- 方式1: 普通的 f-string，输出 'SSE,600000'")
print("- 方式2: f-string 中 {{{{ 表示字面量 {，输出 '{SSE,600000}'")
print("- 方式3: .format() 方法，{{{{ 表示字面量 {，输出 '{SSE,600000}'")

# 测试实际的代码
print("\n实际代码测试:")
stock_info = {
    'tradingview_symbol': f"'{{{exchange},{stock_code}}}'"
}
print(f"tradingview_symbol: {stock_info['tradingview_symbol']}")
