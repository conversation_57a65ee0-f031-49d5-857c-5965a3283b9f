import requests
import pandas as pd
import units
from datetime import datetime

# 设置 pandas 显示选项，显示所有行和列
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.expand_frame_repr', False)

def get_stock_data_from_eastmoney(stock_code, days=30):
    """
    从东方财富获取股票数据

    参数:
    stock_code: 股票代码 (如: '000001')
    days: 获取天数 (默认30天)

    返回:
    DataFrame: 包含日期、开盘价、收盘价、最高价、最低价、成交量、振幅等数据
    """

    def try_get_data(stock_code, exchange_code, days):
        """尝试从指定交易所获取股票数据"""
        import time

        # 多个API接口，提高成功率
        apis = [
            {
                'url': "https://push2his.eastmoney.com/api/qt/stock/kline/get",
                'params': {
                    'secid': f'{exchange_code}.{stock_code}',
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fields1': 'f1,f2,f3,f4,f5,f6',
                    'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                    'klt': '101',  # 日K线
                    'fqt': '1',    # 前复权
                    'end': '209900101',
                    'lmt': days    # 获取天数
                }
            },
            {
                'url': "http://push2his.eastmoney.com/api/qt/stock/kline/get",
                'params': {
                    'secid': f'{exchange_code}.{stock_code}',
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fields1': 'f1,f2,f3,f4,f5,f6',
                    'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                    'klt': '101',
                    'fqt': '1',
                    'end': '209900101',
                    'lmt': days
                }
            }
        ]

        # 设置请求头，模拟浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://quote.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }

        for api_idx, api in enumerate(apis):
            for retry in range(3):  # 每个API重试3次
                try:
                    print(f"  尝试API {api_idx + 1}，第 {retry + 1} 次请求...")

                    response = requests.get(
                        api['url'],
                        params=api['params'],
                        headers=headers,
                        timeout=15
                    )

                    if response.status_code == 200:
                        data = response.json()
                        if 'data' in data and data['data'] and 'klines' in data['data']:
                            klines = data['data']['klines']
                            stock_name = data['data'].get('name', '未知股票')

                            if klines:
                                stock_data = []
                                prev_close = None

                                for i, kline in enumerate(klines):
                                    parts = kline.split(',')
                                    if len(parts) >= 11:
                                        date = parts[0]
                                        open_price = float(parts[1])
                                        close_price = float(parts[2])
                                        high_price = float(parts[3])
                                        low_price = float(parts[4])
                                        volume = float(parts[5])  # 成交量
                                        amount = float(parts[6])  # 成交额

                                        # 计算振幅：(最高价 - 最低价) / 昨收价 * 100%
                                        if i > 0 and prev_close is not None:
                                            amplitude = ((high_price - low_price) / prev_close) * 100
                                        else:
                                            # 第一天使用开盘价作为基准
                                            amplitude = ((high_price - low_price) / open_price) * 100 if open_price > 0 else 0

                                        stock_info = {
                                            '股票代码': stock_code,
                                            '股票名称': stock_name,
                                            '日期': date,
                                            '开盘价': open_price,
                                            '收盘价': close_price,
                                            '最高价': high_price,
                                            '最低价': low_price,
                                            '成交量': int(volume),
                                            '成交额': amount,
                                            '振幅(%)': round(amplitude, 2)
                                        }
                                        stock_data.append(stock_info)
                                        prev_close = close_price

                                print(f"  ✓ 成功获取数据")
                                return stock_data, stock_name

                    print(f"  ❌ API {api_idx + 1} 第 {retry + 1} 次请求失败: HTTP {response.status_code}")

                except Exception as e:
                    print(f"  ❌ API {api_idx + 1} 第 {retry + 1} 次请求出错: {e}")

                # 重试前等待
                if retry < 2:  # 不是最后一次重试
                    time.sleep(1)

            # API间等待
            if api_idx < len(apis) - 1:
                time.sleep(0.5)

        return None, None

    # 先尝试上海证券交易所 (1)
    stock_data, stock_name = try_get_data(stock_code, '1', days)
    exchange_name = '上海证券交易所'

    if not stock_data:
        # 再尝试深圳证券交易所 (0)
        stock_data, stock_name = try_get_data(stock_code, '0', days)
        exchange_name = '深圳证券交易所'

    if stock_data:
        df = pd.DataFrame(stock_data)
        # 按日期排序，最新的在前面
        df = df.sort_values('日期', ascending=False).reset_index(drop=True)
        print(f"✓ 成功从{exchange_name}获取股票 {stock_code}({stock_name}) 的数据")
        return df
    else:
        print(f"❌ 未能获取股票代码 {stock_code} 的数据")
        return None

def get_multiple_stocks_data(stock_codes, days=30):
    """
    获取多只股票的数据

    参数:
    stock_codes: 股票代码列表
    days: 获取天数

    返回:
    dict: 股票代码为key，DataFrame为value的字典
    """
    results = {}

    for i, stock_code in enumerate(stock_codes, 1):
        print(f"\n正在处理第 {i}/{len(stock_codes)} 只股票: {stock_code}")
        df = get_stock_data_from_eastmoney(stock_code, days)
        if df is not None:
            results[stock_code] = df

        # 添加延迟，避免请求过于频繁
        import time
        time.sleep(0.5)

    return results

def main():
    """
    主函数 - 演示如何使用
    """
    print("=" * 80)
    print("从东方财富获取股票数据")
    print("=" * 80)

    # 示例1: 获取单只股票数据
    print("\n示例1: 获取单只股票数据 (平安银行 000001)")
    df = get_stock_data_from_eastmoney('000001', days=10)
    if df is not None:
        print("\n股票数据:")
        print(df)

    # 示例2: 从文件读取股票代码并获取数据
    print("\n\n示例2: 从文件读取股票代码")
    stock_codes = units.read()[:5]  # 只取前5只股票作为演示
    if stock_codes:
        print(f"读取到 {len(stock_codes)} 只股票代码，演示前5只:")
        results = get_multiple_stocks_data(stock_codes, days=5)

        print(f"\n成功获取 {len(results)} 只股票的数据:")
        for stock_code, df in results.items():
            print(f"\n股票代码: {stock_code}")
            print(df.head(3))  # 只显示前3行
    else:
        print("未能读取到股票代码")

if __name__ == "__main__":
    main()
