# 解决 pandas DataFrame 中 datetime 索引换行显示问题的演示
import pandas as pd
import sys
import os
from huobiTradingPair import TvDatafeed
# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# 设置 pandas 显示选项 - 增强版
print("🔧 配置 pandas 显示选项...")
pd.set_option('display.max_rows', None)  # 显示所有行
pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.width', 2000)  # 增加显示宽度
pd.set_option('display.expand_frame_repr', False)  # 禁用自动换行
pd.set_option('display.colheader_justify', 'left')  # 列标题左对齐
pd.set_option('display.max_colwidth', 100)  # 设置列最大宽度
pd.set_option('display.precision', 2)  # 浮点数精度
pd.set_option('display.float_format', '{:.2f}'.format)  # 浮点数格式

tv = TvDatafeed()

# 获取股票数据
data = tv.get_hist('002594', 'SZSE', n_bars=3)

if data is not None:
    print("\n" + "="*100)
    data_reset = data.reset_index()
    print(data_reset.to_string(index=False))
else:
    print("❌ 无法获取任何数据")
