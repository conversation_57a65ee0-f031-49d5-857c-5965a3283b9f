# 自动搜索股票代码 - 先上证后深证
import requests
import pandas as pd
import units

# 设置 pandas 显示选项
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.expand_frame_repr', False)
#初始化stock_code
stock_code = ''
limit = 3
def try_get_stock_data(stock_code, exchange_code):
    """尝试从指定交易所获取股票数据"""
    try:
        # 东方财富网历史数据API
        url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': f'{exchange_code}.{stock_code}',
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': '101',
            'fqt': '1',
            'end': '209900101',
            'lmt': limit
        }
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and data['data'] and 'klines' in data['data']:
                klines = data['data']['klines']
                stock_name = data['data'].get('name', '未知股票')

                if klines:
                    stock_data = []
                    for kline in klines:
                        parts = kline.split(',')
                        if len(parts) >= 11:
                            stock_info = {
                                'date': parts[0],
                                'open': float(parts[1]),
                                'close': float(parts[2]),
                                'high': float(parts[3]),
                                'low': float(parts[4]),
                                '涨幅': float(parts[8]),
                                # '振幅': float(parts[7]),
                                'vol': float(parts[5]),
                                # '成交额': float(parts[6])
                            }
                            stock_data.append(stock_info)
                    return stock_data, stock_name

        return None, None

    except Exception:
        return None, None
def get_stock_recent_data(stock_code):
    # 步骤1: 先在上海证券交易所搜索
    stock_data, stock_name = try_get_stock_data(stock_code, '1')  # 1=上海证券交易所

    if stock_data:
        print(f"✓ 在上海证券交易所找到股票: {stock_code} +':'{+stock_name}")
        return stock_data, stock_name, 'SSE', '上海证券交易所'

    # 步骤2: 如果上证找不到，在深圳证券交易所搜索
    stock_data, stock_name = try_get_stock_data(stock_code, '0')  # 0=深圳证券交易所

    if stock_data:
        print(f"✓ 在深圳证券交易所找到股票: {stock_name}")
        return stock_data, stock_name, 'SZSE', '深圳证券交易所'
    print(f"❌ 两个交易所未找到该股票代码: {stock_code}")
    return None, None, None, None

pattern_list = []
def mapvalue(stock_code):
    stock_data, stock_name, exchange_code, exchange_name = get_stock_recent_data(stock_code)
    if stock_data and len(stock_data) >= 3:
        print(stock_data)
        day1 = stock_data[0]
        day2 = stock_data[1]
        day3 = stock_data[2]

        # 第二天与地一天的特征
        pattern = (day2['close'] < day1['close'] and day2['vol'] < day1['vol'] and units.included_line(day1, day2) == False
                   # 第二天当天的特征
                   and day2['close'] > day2['open'] and day2['close'] > day2['open']
                   # 第三天的特征
                   and day3['close'] > day2['high'] and day3['vol'] > day2['vol'])
        if pattern:
            # return True
            print("True"+stock_code)
            # 输出当前股票代码
            pattern_list.append(stock_code)
        else:
            # return False
            print("False")
            print(stock_code)


def main():
    """主函数"""
    print("先在上海证券交易所搜索，后深圳证券交易所搜索")
    # 获取股票数据

    for stock_code in units.read():
        #打印当前循环次数
        print(f"正在处理第 {units.read().index(stock_code)+1} 只股票")
        mapvalue(stock_code)

if __name__ == "__main__":
    main()
    print(pattern_list)
