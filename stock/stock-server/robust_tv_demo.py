# 改进的 TradingView 数据获取 - 添加错误处理和重试机制
import pandas as pd
import sys
import os
import time
import random

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from huobiTradingPair import TvDatafeed

# 设置 pandas 显示选项
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 2000)
pd.set_option('display.expand_frame_repr', False)

def get_stock_data_with_retry(symbol, exchange, n_bars=5, max_retries=3, delay_range=(1, 3)):
    """
    带重试机制的股票数据获取函数
    
    参数:
    symbol: 股票代码
    exchange: 交易所
    n_bars: 获取的K线数量
    max_retries: 最大重试次数
    delay_range: 重试间隔范围（秒）
    """
    
    for attempt in range(max_retries + 1):
        try:
            print(f"🔄 第 {attempt + 1} 次尝试获取 {symbol} 数据...")
            
            # 每次重新初始化 TvDatafeed
            tv = TvDatafeed()
            
            # 获取数据
            data = tv.get_hist(symbol, exchange, n_bars=n_bars)
            
            if data is not None and not data.empty:
                print(f"✅ 成功获取 {symbol} 数据！")
                return data
            else:
                print(f"⚠️ 获取到空数据")
                
        except BrokenPipeError as e:
            print(f"❌ 网络连接中断: {e}")
        except ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {type(e).__name__}: {e}")
        
        # 如果不是最后一次尝试，等待后重试
        if attempt < max_retries:
            delay = random.uniform(delay_range[0], delay_range[1])
            print(f"⏳ 等待 {delay:.1f} 秒后重试...")
            time.sleep(delay)
        else:
            print(f"💥 所有重试都失败了")
    
    return None

def test_multiple_stocks():
    """测试多只股票的数据获取"""
    
    # 测试股票列表
    test_stocks = [
        ('002594', 'SZSE', '比亚迪'),
        ('000001', 'SZSE', '平安银行'),
        ('600000', 'SSE', '浦发银行'),
        ('688001', 'SSE', '华兴源创')
    ]
    
    print("🚀 开始测试多只股票数据获取")
    print("=" * 80)
    
    success_count = 0
    total_count = len(test_stocks)
    
    for i, (symbol, exchange, name) in enumerate(test_stocks, 1):
        print(f"\n📊 [{i}/{total_count}] 测试 {name} ({symbol})")
        print("-" * 50)
        
        data = get_stock_data_with_retry(symbol, exchange, n_bars=3)
        
        if data is not None:
            success_count += 1
            print(f"📈 {name} 数据预览:")
            print(data.to_string())
        else:
            print(f"💔 {name} 数据获取失败")
        
        # 在请求之间添加延迟，避免频率过高
        if i < total_count:
            delay = random.uniform(2, 4)
            print(f"\n⏸️ 休息 {delay:.1f} 秒...")
            time.sleep(delay)
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果: {success_count}/{total_count} 成功")
    print(f"📈 成功率: {success_count/total_count*100:.1f}%")

def get_single_stock_robust(symbol='002594', exchange='SZSE'):
    """获取单只股票数据的稳健版本"""
    
    print(f"🎯 获取单只股票数据: {symbol} ({exchange})")
    print("=" * 60)
    
    data = get_stock_data_with_retry(symbol, exchange, n_bars=5)
    
    if data is not None:
        print("\n📊 股票数据:")
        print(data.to_string())
        
        print(f"\n📈 数据统计:")
        print(f"   • 数据行数: {len(data)}")
        print(f"   • 数据列数: {len(data.columns)}")
        print(f"   • 最新价格: {data['close'].iloc[-1]:.2f}")
        print(f"   • 价格区间: {data['low'].min():.2f} - {data['high'].max():.2f}")
        
        return data
    else:
        print("💔 数据获取失败")
        return None

if __name__ == "__main__":
    print("🔧 TradingView 数据获取 - 稳健版")
    print("=" * 80)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 单只股票测试")
    print("2. 多只股票测试")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            get_single_stock_robust()
        elif choice == "2":
            test_multiple_stocks()
        else:
            print("默认执行单只股票测试...")
            get_single_stock_robust()
            
    except KeyboardInterrupt:
        print("\n👋 用户中断了程序")
    except Exception as e:
        print(f"\n💥 程序执行出错: {e}")
