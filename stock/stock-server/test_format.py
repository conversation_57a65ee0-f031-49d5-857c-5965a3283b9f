# 测试 f-string 格式化语法
exchange = "SSE"
stock_code = "600000"

# 测试不同的格式化方式
print("测试 f-string 格式化语法：")
print("="*50)

# 方式1：原始格式
format1 = f"'{exchange},{stock_code}'"
print(f"方式1: f\"'{{exchange}},{{stock_code}}'\" = {format1}")

# 方式2：带花括号的格式（错误的方式）
print("方式2: f\"'{{{{exchange}},{{stock_code}}}}\"' = 语法错误（单个}不允许）")

# 方式3：正确的带花括号格式
format3 = f"'{{{exchange},{stock_code}}}'"
print(f"方式3: f\"'{{{{{{exchange}},{{stock_code}}}}}}\"' = {format3}")

# 方式4：使用 .format() 方法
format4 = "'{{{},{}}}'".format(exchange, stock_code)
print(f"方式4: \"'{{{{{{}}},{{{{}}}}}}'\".format() = {format4}")

print("\n预期输出格式: '{SSE,600000}'")
print("="*50)
