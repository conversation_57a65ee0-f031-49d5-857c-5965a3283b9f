# 导入必要的库
import pandas as pd
import requests
import json
from huobiTradingPair import TvDatafeed, Interval

# 设置 pandas 显示选项
pd.set_option('display.max_rows', None)  # 显示所有行
pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.width', 1000)  # 加宽显示宽度
pd.set_option('display.expand_frame_repr', False)  # 不换行显示

# 方法1: 使用火币交易所的官方API
def get_huobi_symbols_from_api():
    try:
        # 火币API获取所有交易对
        url = "https://api.huobi.pro/v1/common/symbols"
        response = requests.get(url)
        data = response.json()
        
        if data['status'] == 'ok':
            symbols = []
            for item in data['data']:
                # 构建交易对名称
                symbol = f"{item['base-currency'].upper()}{item['quote-currency'].upper()}"
                symbols.append({
                    'symbol': symbol,
                    'base_currency': item['base-currency'],
                    'quote_currency': item['quote-currency'],
                    'state': item['state']
                })
            
            # 转换为DataFrame并只保留在线的交易对
            df = pd.DataFrame(symbols)
            online_df = df[df['state'] == 'online']
            
            print(f"找到 {len(online_df)} 个在线交易对")
            return online_df
        else:
            print(f"API请求失败: {data['err-msg']}")
            return None
    except Exception as e:
        print(f"获取火币交易对时出错: {e}")
        return None


# 方法1: 使用火币API
huobi_symbols_api = get_huobi_symbols_from_api()
if huobi_symbols_api is not None and not huobi_symbols_api.empty:
    print(huobi_symbols_api[['symbol', 'base_currency', 'quote_currency']].head(10000))
    
    # 统计不同计价货币的数量
    quote_counts = huobi_symbols_api['quote_currency'].value_counts()
    print("\n按计价货币统计交易对数量:")
    print(quote_counts)
    
    # 保存到CSV文件
    # csv_file = "huobi_symbols.csv"
    # huobi_symbols_api.to_csv(csv_file, index=False)
    # print(f"\n所有交易对已保存到 {csv_file}")