import subprocess

import pandas as pd
import sys
import os
import ssl
import urllib3
# 禁用SSL警告（仅用于测试环境）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置SSL上下文，解决Python 3.13.4的SSL证书验证问题
try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

# 设置环境变量来解决SSL问题
os.environ['PYTHONHTTPSVERIFY'] = '0'
os.environ['SSL_VERIFY'] = 'false'

# 对于macOS，尝试安装证书
import subprocess
try:
    # 检查是否在macOS上
    if sys.platform == 'darwin':
        print("ℹ️ 检测到macOS系统，尝试安装SSL证书...")
        # 尝试运行证书安装命令
        cert_command = '/Applications/Python 3.13/Install Certificates.command'
        if os.path.exists('/Applications/Python 3.13/Install Certificates.command'):
            subprocess.run([cert_command], shell=True, capture_output=True)
            print("✓ 尝试安装证书完成")
except Exception as e:
    print(f"ℹ️ 证书安装失败，继续使用不验证模式: {e}")